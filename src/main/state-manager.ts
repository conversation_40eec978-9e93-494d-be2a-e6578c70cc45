import { diagnosticsService } from "./diagnostics"

export type AppMode = 'normal' | 'recording' | 'textInput' | 'agent'

export interface AppState {
  // Core mode - only one can be active at a time
  mode: AppMode

  // Recording state
  isRecording: boolean
  focusedAppBeforeRecording: string | null

  // Agent mode specific state
  agentMode: {
    isActive: boolean
    currentIteration: number
    maxIterations: number
  }

  // Panel state
  panel: {
    isVisible: boolean
  }

  // MCP processing state
  mcp: {
    isProcessing: boolean
    activeToolCalls: number
  }
}

type StateListener = (state: AppState, previousState: AppState) => void
type StateValidator = (newState: Partial<AppState>, currentState: AppState) => string | null

class StateManager {
  private state: AppState = {
    mode: 'normal',
    isRecording: false,
    focusedAppBeforeRecording: null,
    agentMode: {
      isActive: false,
      currentIteration: 0,
      maxIterations: 10
    },
    panel: {
      isVisible: false
    },
    mcp: {
      isProcessing: false,
      activeToolCalls: 0
    }
  }

  private listeners = new Set<StateListener>()
  private validators = new Set<StateValidator>()

  /**
   * Get current state (immutable copy)
   */
  getState(): AppState {
    return JSON.parse(JSON.stringify(this.state))
  }

  /**
   * Update state with validation and notifications
   */
  setState(updates: Partial<AppState>, context?: string): boolean {
    const previousState = this.getState()
    const newState = { ...this.state, ...updates }

    // Run validators
    for (const validator of this.validators) {
      const error = validator(updates, this.state)
      if (error) {
        diagnosticsService.logError('state-manager', `State validation failed: ${error}`, {
          context,
          updates,
          currentState: this.state
        })
        return false
      }
    }

    // Apply updates
    this.state = newState

    // Notify listeners
    this.notifyListeners(this.state, previousState, context)

    return true
  }

  /**
   * Update nested state properties safely
   */
  updateNested<K extends keyof AppState>(
    key: K,
    updates: Partial<AppState[K]>,
    context?: string
  ): boolean {
    const currentValue = this.state[key]
    const newValue = typeof currentValue === 'object' && currentValue !== null
      ? { ...currentValue, ...updates }
      : updates

    return this.setState({ [key]: newValue } as Partial<AppState>, context)
  }

  /**
   * Subscribe to state changes
   */
  subscribe(listener: StateListener): () => void {
    this.listeners.add(listener)
    return () => this.listeners.delete(listener)
  }

  /**
   * Add state validator
   */
  addValidator(validator: StateValidator): () => void {
    this.validators.add(validator)
    return () => this.validators.delete(validator)
  }

  /**
   * Atomic state transitions for complex operations
   */
  async transaction<T>(
    operation: (currentState: AppState) => Promise<{ updates: Partial<AppState>; result: T }>,
    context?: string
  ): Promise<T | null> {
    const currentState = this.getState()

    try {
      const { updates, result } = await operation(currentState)
      const success = this.setState(updates, context)
      return success ? result : null
    } catch (error) {
      diagnosticsService.logError('state-manager', 'Transaction failed', {
        context,
        error,
        currentState
      })
      return null
    }
  }

  private notifyListeners(newState: AppState, previousState: AppState, context?: string) {
    for (const listener of this.listeners) {
      try {
        listener(newState, previousState)
      } catch (error) {
        diagnosticsService.logError('state-manager', 'Listener error', {
          context,
          error
        })
      }
    }
  }

  /**
   * Debug helper to log state changes
   */
  enableDebugLogging() {
    this.subscribe((newState, previousState) => {
      const changes = this.getStateChanges(previousState, newState)
      if (Object.keys(changes).length > 0) {
        console.log('State changed:', changes)
      }
    })
  }

  private getStateChanges(oldState: AppState, newState: AppState): Record<string, any> {
    const changes: Record<string, any> = {}

    for (const key in newState) {
      if (JSON.stringify(oldState[key as keyof AppState]) !== JSON.stringify(newState[key as keyof AppState])) {
        changes[key] = {
          from: oldState[key as keyof AppState],
          to: newState[key as keyof AppState]
        }
      }
    }

    return changes
  }
}

// Create singleton instance
export const stateManager = new StateManager()

// Add default validators
stateManager.addValidator((updates, currentState) => {
  // Validate mode transitions
  if (updates.mode) {
    const newMode = updates.mode
    const currentMode = currentState.mode

    // Define valid mode transitions
    const validTransitions: Record<AppMode, AppMode[]> = {
      'normal': ['recording', 'textInput', 'agent'],
      'recording': ['normal'],
      'textInput': ['normal', 'agent'], // Allow text input to trigger agent mode
      'agent': ['normal']
    }

    if (!validTransitions[currentMode].includes(newMode)) {
      return `Invalid mode transition from ${currentMode} to ${newMode}`
    }
  }

  // Agent mode requires panel to be visible
  if (updates.agentMode?.isActive && !(updates.panel?.isVisible ?? currentState.panel.isVisible)) {
    return "Agent mode requires panel to be visible"
  }

  // Mode consistency checks
  if (updates.mode === 'agent' && !(updates.agentMode?.isActive ?? currentState.agentMode.isActive)) {
    return "Agent mode requires agentMode.isActive to be true"
  }

  if (updates.mode === 'recording' && !(updates.isRecording ?? currentState.isRecording)) {
    return "Recording mode requires isRecording to be true"
  }

  // Panel visibility consistency
  if (updates.mode && ['recording', 'textInput', 'agent'].includes(updates.mode)) {
    if (updates.panel?.isVisible === false) {
      return `${updates.mode} mode requires panel to be visible`
    }
  }

  // Agent mode iteration bounds
  if (updates.agentMode?.currentIteration !== undefined) {
    const maxIterations = updates.agentMode.maxIterations ?? currentState.agentMode.maxIterations
    if (updates.agentMode.currentIteration < 0 || updates.agentMode.currentIteration > maxIterations) {
      return `Agent iteration must be between 0 and ${maxIterations}`
    }
  }

  // MCP tool calls bounds
  if (updates.mcp?.activeToolCalls !== undefined && updates.mcp.activeToolCalls < 0) {
    return "Active tool calls cannot be negative"
  }

  return null
})

// Add additional validators for edge cases
stateManager.addValidator((updates, currentState) => {
  // Prevent starting recording while agent mode is active
  if (updates.isRecording && currentState.agentMode.isActive) {
    return "Cannot start recording while agent mode is active"
  }

  // Prevent starting agent mode while recording
  if (updates.agentMode?.isActive && currentState.isRecording) {
    return "Cannot start agent mode while recording"
  }

  // Ensure MCP processing state is consistent
  if (updates.mcp?.isProcessing === false && (updates.mcp?.activeToolCalls ?? currentState.mcp.activeToolCalls) > 0) {
    return "Cannot stop MCP processing while tool calls are active"
  }

  return null
})

// Add validator for state cleanup consistency
stateManager.addValidator((updates, currentState) => {
  // When transitioning to normal mode, ensure cleanup
  if (updates.mode === 'normal') {
    const hasActiveStates =
      (updates.isRecording ?? currentState.isRecording) ||
      (updates.agentMode?.isActive ?? currentState.agentMode.isActive) ||
      (updates.mcp?.isProcessing ?? currentState.mcp.isProcessing)

    if (hasActiveStates) {
      return "Cannot transition to normal mode while other states are active"
    }
  }

  return null
})

// Helper functions for common state operations
export const stateHelpers = {
  startRecording: (focusedApp?: string) =>
    stateManager.setState({
      mode: 'recording',
      isRecording: true,
      focusedAppBeforeRecording: focusedApp || null,
      panel: { isVisible: true }
    }, 'startRecording'),

  stopRecording: () =>
    stateManager.setState({
      mode: 'normal',
      isRecording: false,
      panel: { isVisible: false }
    }, 'stopRecording'),

  startTextInput: () =>
    stateManager.setState({
      mode: 'textInput',
      isRecording: false,
      panel: { isVisible: true }
    }, 'startTextInput'),

  stopTextInput: () =>
    stateManager.setState({
      mode: 'normal',
      panel: { isVisible: false }
    }, 'stopTextInput'),

  startAgentMode: (maxIterations: number = 10) =>
    stateManager.setState({
      mode: 'agent',
      agentMode: { isActive: true, currentIteration: 0, maxIterations },
      panel: { isVisible: true }
    }, 'startAgentMode'),

  stopAgentMode: () =>
    stateManager.setState({
      mode: 'normal',
      agentMode: { isActive: false, currentIteration: 0, maxIterations: 10 },
      panel: { isVisible: false }
    }, 'stopAgentMode'),

  updateAgentIteration: (iteration: number) =>
    stateManager.updateNested('agentMode', { currentIteration: iteration }, 'updateAgentIteration'),

  startMcpProcessing: () =>
    stateManager.updateNested('mcp', { isProcessing: true }, 'startMcpProcessing'),

  stopMcpProcessing: () =>
    stateManager.updateNested('mcp', { isProcessing: false, activeToolCalls: 0 }, 'stopMcpProcessing'),

  incrementToolCalls: () => {
    const currentState = stateManager.getState()
    return stateManager.updateNested('mcp', {
      activeToolCalls: currentState.mcp.activeToolCalls + 1
    }, 'incrementToolCalls')
  },

  decrementToolCalls: () => {
    const currentState = stateManager.getState()
    return stateManager.updateNested('mcp', {
      activeToolCalls: Math.max(0, currentState.mcp.activeToolCalls - 1)
    }, 'decrementToolCalls')
  },

  // Convenience methods for mode checking
  isInMode: (mode: AppMode): boolean => {
    return stateManager.getState().mode === mode
  },

  isTextInputActive: (): boolean => {
    return stateManager.getState().mode === 'textInput'
  },

  isAgentModeActive: (): boolean => {
    return stateManager.getState().mode === 'agent'
  },

  isRecordingActive: (): boolean => {
    return stateManager.getState().mode === 'recording'
  },

  // Error recovery helpers
  forceReset: (): boolean => {
    diagnosticsService.logWarning('state-manager', 'Force resetting state to normal mode')
    return stateManager.setState({
      mode: 'normal',
      isRecording: false,
      agentMode: { isActive: false, currentIteration: 0, maxIterations: 10 },
      panel: { isVisible: false },
      mcp: { isProcessing: false, activeToolCalls: 0 }
    }, 'forceReset')
  },

  emergencyCleanup: (): boolean => {
    const currentState = stateManager.getState()
    diagnosticsService.logWarning('state-manager', 'Emergency cleanup triggered', { currentState })

    // Force cleanup of all active states
    const updates: Partial<AppState> = {}

    if (currentState.mode !== 'normal') {
      updates.mode = 'normal'
    }

    if (currentState.isRecording) {
      updates.isRecording = false
    }

    if (currentState.agentMode.isActive) {
      updates.agentMode = { isActive: false, currentIteration: 0, maxIterations: 10 }
    }

    if (currentState.mcp.isProcessing || currentState.mcp.activeToolCalls > 0) {
      updates.mcp = { isProcessing: false, activeToolCalls: 0 }
    }

    if (currentState.panel.isVisible) {
      updates.panel = { isVisible: false }
    }

    return Object.keys(updates).length > 0 ? stateManager.setState(updates, 'emergencyCleanup') : true
  }
}
