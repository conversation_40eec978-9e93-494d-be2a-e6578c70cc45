import { diagnosticsService } from "./diagnostics"

export interface AppState {
  isRecording: boolean
  isTextInputActive: boolean
  focusedAppBeforeRecording: string | null
  agentMode: {
    isActive: boolean
    currentIteration: number
    maxIterations: number
  }
  panel: {
    isVisible: boolean
    mode: 'normal' | 'agent' | 'textInput'
  }
  mcp: {
    isProcessing: boolean
    activeToolCalls: number
  }
}

type StateListener = (state: AppState, previousState: AppState) => void
type StateValidator = (newState: Partial<AppState>, currentState: AppState) => string | null

class StateManager {
  private state: AppState = {
    isRecording: false,
    isTextInputActive: false,
    focusedAppBeforeRecording: null,
    agentMode: {
      isActive: false,
      currentIteration: 0,
      maxIterations: 10
    },
    panel: {
      isVisible: false,
      mode: 'normal'
    },
    mcp: {
      isProcessing: false,
      activeToolCalls: 0
    }
  }

  private listeners = new Set<StateListener>()
  private validators = new Set<StateValidator>()

  /**
   * Get current state (immutable copy)
   */
  getState(): AppState {
    return JSON.parse(JSON.stringify(this.state))
  }

  /**
   * Update state with validation and notifications
   */
  setState(updates: Partial<AppState>, context?: string): boolean {
    const previousState = this.getState()
    const newState = { ...this.state, ...updates }

    // Run validators
    for (const validator of this.validators) {
      const error = validator(updates, this.state)
      if (error) {
        diagnosticsService.logError('state-manager', `State validation failed: ${error}`, {
          context,
          updates,
          currentState: this.state
        })
        return false
      }
    }

    // Apply updates
    this.state = newState

    // Notify listeners
    this.notifyListeners(this.state, previousState, context)

    return true
  }

  /**
   * Update nested state properties safely
   */
  updateNested<K extends keyof AppState>(
    key: K,
    updates: Partial<AppState[K]>,
    context?: string
  ): boolean {
    const currentValue = this.state[key]
    const newValue = typeof currentValue === 'object' && currentValue !== null
      ? { ...currentValue, ...updates }
      : updates

    return this.setState({ [key]: newValue } as Partial<AppState>, context)
  }

  /**
   * Subscribe to state changes
   */
  subscribe(listener: StateListener): () => void {
    this.listeners.add(listener)
    return () => this.listeners.delete(listener)
  }

  /**
   * Add state validator
   */
  addValidator(validator: StateValidator): () => void {
    this.validators.add(validator)
    return () => this.validators.delete(validator)
  }

  /**
   * Atomic state transitions for complex operations
   */
  async transaction<T>(
    operation: (currentState: AppState) => Promise<{ updates: Partial<AppState>; result: T }>,
    context?: string
  ): Promise<T | null> {
    const currentState = this.getState()
    
    try {
      const { updates, result } = await operation(currentState)
      const success = this.setState(updates, context)
      return success ? result : null
    } catch (error) {
      diagnosticsService.logError('state-manager', 'Transaction failed', {
        context,
        error,
        currentState
      })
      return null
    }
  }

  private notifyListeners(newState: AppState, previousState: AppState, context?: string) {
    for (const listener of this.listeners) {
      try {
        listener(newState, previousState)
      } catch (error) {
        diagnosticsService.logError('state-manager', 'Listener error', {
          context,
          error
        })
      }
    }
  }

  /**
   * Debug helper to log state changes
   */
  enableDebugLogging() {
    this.subscribe((newState, previousState) => {
      const changes = this.getStateChanges(previousState, newState)
      if (Object.keys(changes).length > 0) {
        console.log('State changed:', changes)
      }
    })
  }

  private getStateChanges(oldState: AppState, newState: AppState): Record<string, any> {
    const changes: Record<string, any> = {}
    
    for (const key in newState) {
      if (JSON.stringify(oldState[key as keyof AppState]) !== JSON.stringify(newState[key as keyof AppState])) {
        changes[key] = {
          from: oldState[key as keyof AppState],
          to: newState[key as keyof AppState]
        }
      }
    }
    
    return changes
  }
}

// Create singleton instance
export const stateManager = new StateManager()

// Add default validators
stateManager.addValidator((updates, currentState) => {
  // Can't be recording and in text input mode simultaneously
  if (updates.isRecording && (updates.isTextInputActive ?? currentState.isTextInputActive)) {
    return "Cannot be recording and in text input mode simultaneously"
  }
  
  // Agent mode requires panel to be visible
  if (updates.agentMode?.isActive && !(updates.panel?.isVisible ?? currentState.panel.isVisible)) {
    return "Agent mode requires panel to be visible"
  }
  
  return null
})

// Helper functions for common state operations
export const stateHelpers = {
  startRecording: (focusedApp?: string) => 
    stateManager.setState({
      isRecording: true,
      isTextInputActive: false,
      focusedAppBeforeRecording: focusedApp || null,
      panel: { isVisible: true, mode: 'normal' }
    }, 'startRecording'),

  stopRecording: () =>
    stateManager.setState({
      isRecording: false,
      panel: { isVisible: false, mode: 'normal' }
    }, 'stopRecording'),

  startTextInput: () =>
    stateManager.setState({
      isTextInputActive: true,
      isRecording: false,
      panel: { isVisible: true, mode: 'textInput' }
    }, 'startTextInput'),

  stopTextInput: () =>
    stateManager.setState({
      isTextInputActive: false,
      panel: { isVisible: false, mode: 'normal' }
    }, 'stopTextInput'),

  startAgentMode: (maxIterations: number = 10) =>
    stateManager.setState({
      agentMode: { isActive: true, currentIteration: 0, maxIterations },
      panel: { isVisible: true, mode: 'agent' }
    }, 'startAgentMode'),

  stopAgentMode: () =>
    stateManager.setState({
      agentMode: { isActive: false, currentIteration: 0, maxIterations: 10 },
      panel: { isVisible: false, mode: 'normal' }
    }, 'stopAgentMode'),

  updateAgentIteration: (iteration: number) =>
    stateManager.updateNested('agentMode', { currentIteration: iteration }, 'updateAgentIteration'),

  startMcpProcessing: () =>
    stateManager.updateNested('mcp', { isProcessing: true }, 'startMcpProcessing'),

  stopMcpProcessing: () =>
    stateManager.updateNested('mcp', { isProcessing: false, activeToolCalls: 0 }, 'stopMcpProcessing'),

  incrementToolCalls: () => {
    const currentState = stateManager.getState()
    return stateManager.updateNested('mcp', { 
      activeToolCalls: currentState.mcp.activeToolCalls + 1 
    }, 'incrementToolCalls')
  },

  decrementToolCalls: () => {
    const currentState = stateManager.getState()
    return stateManager.updateNested('mcp', { 
      activeToolCalls: Math.max(0, currentState.mcp.activeToolCalls - 1) 
    }, 'decrementToolCalls')
  }
}
