import { app } from "electron"
import { stateManager } from "./state-manager"
import { stateSyncService } from "./state-sync"
import { diagnosticsService } from "./diagnostics"

/**
 * Initialize the application with centralized state management
 */
export function initializeApp() {
  // Enable debug logging for state changes in development
  if (process.env.NODE_ENV === 'development') {
    stateManager.enableDebugLogging()
  }

  // Initialize state synchronization service
  stateSyncService.initialize()

  // Add application-level state validators
  stateManager.addValidator((updates, currentState) => {
    // Ensure panel visibility matches active modes
    if ((updates.mode === 'recording' || updates.mode === 'textInput' || updates.mode === 'agent') &&
        updates.panel?.isVisible === false) {
      return "Panel must be visible when in active mode"
    }

    return null
  })

  // Subscribe to critical state changes for logging
  stateManager.subscribe((newState, previousState) => {
    // Log mode transitions
    if (newState.isRecording !== previousState.isRecording) {
      diagnosticsService.logInfo('app', `Recording state changed: ${newState.isRecording}`)
    }

    if (newState.agentMode.isActive !== previousState.agentMode.isActive) {
      diagnosticsService.logInfo('app', `Agent mode changed: ${newState.agentMode.isActive}`)
    }

    if (newState.mode !== previousState.mode) {
      diagnosticsService.logInfo('app', `Mode changed: ${previousState.mode} -> ${newState.mode}`)
    }
  })

  // Handle app quit - clean up state
  app.on('before-quit', () => {
    const currentState = stateManager.getState()

    // Log final state for debugging
    diagnosticsService.logInfo('app', 'Application shutting down', {
      finalState: currentState
    })

    // Clean up any active operations
    if (currentState.isRecording) {
      diagnosticsService.logWarning('app', 'Application quit while recording was active')
    }

    if (currentState.agentMode.isActive) {
      diagnosticsService.logWarning('app', 'Application quit while agent mode was active')
    }
  })
}

/**
 * Get current application state for debugging
 */
export function getAppState() {
  return stateManager.getState()
}

/**
 * Force sync state to all renderer processes
 */
export function forceSyncState() {
  stateSyncService.forceSyncAll()
}
