import { getRendererHand<PERSON> } from "@egoist/tipc/main"
import { WINDOWS } from "./window"
import { RendererHandlers } from "./renderer-handlers"
import { stateManager, AppState } from "./state-manager"
import { diagnosticsService } from "./diagnostics"

/**
 * State synchronization service to keep renderer processes informed about state changes
 */
class StateSyncService {
  private isInitialized = false

  /**
   * Initialize state synchronization
   */
  initialize() {
    if (this.isInitialized) return

    // Subscribe to state changes and sync with renderer processes
    stateManager.subscribe((newState, previousState) => {
      this.syncStateToRenderers(newState, previousState)
    })

    this.isInitialized = true
  }

  /**
   * Sync state changes to all renderer processes
   */
  private syncStateToRenderers(newState: AppState, previousState: AppState) {
    // Sync to panel window
    this.syncToPanelWindow(newState, previousState)

    // Sync to main window
    this.syncToMainWindow(newState, previousState)
  }

  /**
   * Sync relevant state to panel window
   */
  private syncToPanelWindow(newState: AppState, previousState: AppState) {
    const panel = WINDOWS.get("panel")
    if (!panel?.webContents) return

    try {
      const handlers = getRendererHandlers<RendererHandlers>(panel.webContents)

      // Handle mode changes
      if (newState.mode !== previousState.mode) {
        // Handle transitions from previous mode
        if (previousState.mode === 'recording') {
          handlers.stopRecording.send()
        } else if (previousState.mode === 'textInput') {
          handlers.hideTextInput.send()
        } else if (previousState.mode === 'agent') {
          handlers.clearAgentProgress.send()
        }

        // Handle transitions to new mode
        if (newState.mode === 'recording') {
          handlers.startRecording.send()
        } else if (newState.mode === 'textInput') {
          handlers.showTextInput.send()
        }
        // Note: Agent mode is handled by the agent progress updates, not mode transitions
      }

      // Handle agent mode changes (independent of mode for cleanup)
      if (newState.agentMode.isActive !== previousState.agentMode.isActive) {
        if (!newState.agentMode.isActive) {
          handlers.clearAgentProgress.send()
        }
      }

    } catch (error) {
      diagnosticsService.logError('state-sync', 'Failed to sync state to panel window', error)
    }
  }

  /**
   * Sync relevant state to main window
   */
  private syncToMainWindow(newState: AppState, previousState: AppState) {
    const main = WINDOWS.get("main")
    if (!main?.webContents) return

    try {
      const handlers = getRendererHandlers<RendererHandlers>(main.webContents)

      // Handle recording history refresh when recording stops
      if (previousState.mode === 'recording' && newState.mode !== 'recording') {
        handlers.refreshRecordingHistory.send()
      }

    } catch (error) {
      diagnosticsService.logError('state-sync', 'Failed to sync state to main window', error)
    }
  }

  /**
   * Force sync current state to all renderers
   */
  forceSyncAll() {
    const currentState = stateManager.getState()
    const emptyState: AppState = {
      mode: 'normal',
      isRecording: false,
      focusedAppBeforeRecording: null,
      agentMode: {
        isActive: false,
        currentIteration: 0,
        maxIterations: 10
      },
      panel: {
        isVisible: false
      },
      mcp: {
        isProcessing: false,
        activeToolCalls: 0
      }
    }

    this.syncStateToRenderers(currentState, emptyState)
  }

  /**
   * Get current state for debugging
   */
  getCurrentState(): AppState {
    return stateManager.getState()
  }
}

// Create singleton instance
export const stateSyncService = new StateSyncService()

// Helper functions for common state operations with automatic sync
export const stateSyncHelpers = {
  /**
   * Start recording with automatic window management
   */
  async startRecording(focusedApp?: string): Promise<boolean> {
    const success = await stateManager.transaction(async (currentState) => {
      if (currentState.mode !== 'normal') {
        throw new Error(`Cannot start recording: currently in ${currentState.mode} mode`)
      }

      return {
        updates: {
          mode: 'recording' as const,
          isRecording: true,
          focusedAppBeforeRecording: focusedApp || null,
          panel: { isVisible: true }
        },
        result: true
      }
    }, 'startRecording')

    return success !== null
  },

  /**
   * Stop recording with cleanup
   */
  async stopRecording(): Promise<boolean> {
    const success = await stateManager.transaction(async (currentState) => {
      if (!currentState.isRecording) {
        throw new Error("Cannot stop recording: not currently recording")
      }

      return {
        updates: {
          isRecording: false,
          focusedAppBeforeRecording: null,
          panel: { isVisible: false, mode: 'normal' as const }
        },
        result: true
      }
    }, 'stopRecording')

    return success !== null
  },

  /**
   * Start text input mode
   */
  async startTextInput(): Promise<boolean> {
    const success = await stateManager.transaction(async (currentState) => {
      if (currentState.mode !== 'normal') {
        throw new Error(`Cannot start text input: currently in ${currentState.mode} mode`)
      }

      return {
        updates: {
          mode: 'textInput' as const,
          panel: { isVisible: true }
        },
        result: true
      }
    }, 'startTextInput')

    return success !== null
  },

  /**
   * Stop text input mode
   */
  async stopTextInput(): Promise<boolean> {
    const success = await stateManager.transaction(async (currentState) => {
      if (currentState.mode !== 'textInput') {
        throw new Error("Cannot stop text input: not currently in text input mode")
      }

      return {
        updates: {
          mode: 'normal' as const,
          panel: { isVisible: false }
        },
        result: true
      }
    }, 'stopTextInput')

    return success !== null
  },

  /**
   * Start agent mode with proper initialization
   */
  async startAgentMode(maxIterations: number = 10): Promise<boolean> {
    const success = await stateManager.transaction(async () => {
      return {
        updates: {
          agentMode: { isActive: true, currentIteration: 0, maxIterations },
          panel: { isVisible: true, mode: 'agent' as const }
        },
        result: true
      }
    }, 'startAgentMode')

    return success !== null
  },

  /**
   * Stop agent mode with cleanup
   */
  async stopAgentMode(): Promise<boolean> {
    const success = await stateManager.transaction(async () => {
      return {
        updates: {
          agentMode: { isActive: false, currentIteration: 0, maxIterations: 10 },
          panel: { isVisible: false, mode: 'normal' as const },
          mcp: { isProcessing: false, activeToolCalls: 0 }
        },
        result: true
      }
    }, 'stopAgentMode')

    return success !== null
  }
}
